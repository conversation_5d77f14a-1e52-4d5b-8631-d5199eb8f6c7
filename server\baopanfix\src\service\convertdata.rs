use server_protoes::{MsgType, OrderMsg, RouterMsg};

pub async fn order_from_router_to_fix(router_msg: &RouterMsg) -> Option<OrderMsg> {
    //只处理Order消息
    if router_msg.msg_type() != MsgType::Order {
        return None;
    }
    let msg_content = match &router_msg.msg_content {
        Some(content) => content,
        None => return None,
    };
    let order_msg = match &msg_content.order_msg {
        Some(msg) => msg,
        None => return None,
    };
    Some(order_msg.clone())
}

// pub async fn exec_from_api_to_router(api_exec: &OrderEvent) -> Option<RouterMsg> {}
