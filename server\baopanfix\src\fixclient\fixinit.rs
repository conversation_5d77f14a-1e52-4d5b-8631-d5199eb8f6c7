use quickfix::*;
use std::{thread, time::Duration};

use crate::{TFixTradeClient, YhFixTradeClient};

pub trait FixClientTrait {
    fn run(&self) {
        loop {
            thread::sleep(Duration::from_secs(10));
        }
    }
}

#[derive(Default)]
pub struct FixTradeSer;

impl FixTradeSer {
    /// Initializes a FIX client with the provided configuration file.
    /// 泛型函数，初始化一个fix客户端
    pub fn init_fix_client<C: ApplicationCallback + 'static + FixClientTrait>(fix_client: &C, config_file: &str) -> anyhow::Result<()> {
        let settings = SessionSettings::try_from_path(config_file)?;
        let store_factory = FileMessageStoreFactory::try_new(&settings)?;
        let log_factory = LogFactory::try_new(&StdLogger::Stdout)?;
        let app = Application::try_new(fix_client)?;

        let mut initiator = SocketInitiator::try_new(&settings, &app, &store_factory, &log_factory)?;

        initiator.start()?;
        fix_client.run();
        initiator.stop()?;
        Ok(())
    }

    pub fn init_tfix(fix_client: &TFixTradeClient, config_file: &str) -> anyhow::Result<()> {
        let settings = SessionSettings::try_from_path(config_file)?;
        let store_factory = FileMessageStoreFactory::try_new(&settings)?;
        let log_factory = LogFactory::try_new(&StdLogger::Stdout)?;
        let app = Application::try_new(fix_client)?;

        let mut initiator = SocketInitiator::try_new(&settings, &app, &store_factory, &log_factory)?;

        initiator.start()?;
        fix_client.run();
        initiator.stop()?;
        Ok(())
    }

    pub fn init_yhtfix(fix_client: &YhFixTradeClient, config_file: &str) -> anyhow::Result<()> {
        let settings = SessionSettings::try_from_path(config_file)?;
        let store_factory = FileMessageStoreFactory::try_new(&settings)?;
        let log_factory = LogFactory::try_new(&StdLogger::Stdout)?;
        let app = Application::try_new(fix_client)?;

        let mut initiator = SocketInitiator::try_new(&settings, &app, &store_factory, &log_factory)?;

        initiator.start()?;
        fix_client.run();
        initiator.stop()?;
        Ok(())
    }
}
