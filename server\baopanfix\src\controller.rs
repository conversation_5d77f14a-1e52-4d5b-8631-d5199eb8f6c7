use std::sync::Arc;

use server_protoes::OrderMsg;

use crate::{TFixTradeClient, YhFixTradeClient};

pub struct TradeController {
    pub tfix: Arc<TFixTradeClient>,
    pub yhfix: Arc<YhFixTradeClient>,
}

impl TradeController {
    pub async fn send_fix(&self, request: OrderMsg) {
        match request.order_type() {
            server_protoes::OrderType::Place => self.place_order(request).await,
            server_protoes::OrderType::Cancel => self.cancel_order(request).await,
            _ => {}
        }
    }

    pub async fn place_order(&self, request: OrderMsg) {
        //根据不同的channel_id，往不同的上手去报
        match request.channel_id {
            1 => {
                let _ = self.tfix._req_new_single_order(&request);
            }
            2 => {
                let _ = self.yhfix._req_new_single_order(&request);
            }
            _ => {}
        }
    }

    pub async fn cancel_order(&self, request: OrderMsg) {
        //根据不同的channel_id，往不同的上手去报
        match request.channel_id {
            1 => {
                let _ = self.tfix._req_order_cancel(&request);
            }
            2 => {
                let _ = self.yhfix._req_order_cancel(&request);
            }
            _ => {}
        }
    }
}
