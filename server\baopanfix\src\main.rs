mod config;
mod controller;
mod fixclient;
mod server;
mod service;

pub use config::*;
pub use controller::*;
pub use fixclient::*;
pub use server::*;
pub use service::*;

use std::error::Error;
use tracing::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    let prefix = "fixclient";
    let dir = "./logs";
    let _guard = server_initialize::initialize_tracing(prefix, dir);

    let setting = Settings::init("config/fix.toml")?;
    info!("config: {:#?}", setting);

    ServerHandler::run(&setting).await;

    Ok(())
}
