use once_cell::sync::OnceCell;
// use quickfix::Message;
use quickfix_msg42::{ExecutionReport, OrderCancelReject};
use server_protoes::{ExecMsg, ExecType, MsgContent, MsgType, RouterMsg};
use tracing::info;

pub static TRADEHANDLER: OnceCell<FixTradeRspHandler> = OnceCell::new();

#[derive(Clone, Debug)]
pub struct FixTradeRspHandler {
    pub tx_repay_to_router: async_channel::Sender<RouterMsg>,
}

pub fn init_trade_handler(handler: &FixTradeRspHandler) {
    info!("初始化handler");
    TRADEHANDLER.set(handler.to_owned()).expect("初始化handler失败");
}

impl FixTradeRspHandler {
    /// 处理fix的响应消息
    pub fn process_fix_new_order_rsp(&self, msg: &ExecutionReport) {
        let mut resp = RouterMsg::default();
        let mut exec_msg = ExecMsg::default();
        let mut msg_content = MsgContent::default();

        let cl_ord_id = msg.get_cl_ord_id().unwrap_or_default(); // 客户订单号
        let side = msg.get_side(); // 买卖方向
        let order_id = msg.get_order_id(); // 柜台合同号
        let last_px = msg.get_last_px(); // 最近一笔成交价格
        let last_shares = msg.get_last_shares(); // 最近一笔成交数量
        let order_qty = msg.get_order_qty(); // 委托数量
        let deal_qty = msg.get_cum_qty(); // 累计成交数量
        let exec_id = msg.get_exec_id(); // 执行编号
        let test = msg.get_text(); // 备注

        exec_msg.order_id = cl_ord_id.parse().unwrap_or_default();
        exec_msg.order_direction = side as i32;
        // exec_msg.channel_id = 0;
        // exec_msg.channel_type = 1;

        //订单状态
        match msg.get_ord_status() {
            quickfix_msg42::field_types::OrdStatus::New => {
                info!("委托下单确认");
                exec_msg.brk_order_id = order_id.parse().unwrap_or_default();
            }
            // quickfix_msg42::field_types::OrdStatus::PartiallyFilled => todo!(),
            quickfix_msg42::field_types::OrdStatus::Filled | quickfix_msg42::field_types::OrdStatus::PartiallyFilled =>
            // | quickfix_msg42::field_types::OrdStatus::PendingCancel
            //     if msg.get_exec_type() == quickfix_msg42::field_types::ExecType::PartialFill
            //         || msg.get_exec_type() == quickfix_msg42::field_types::ExecType::Fill =>
            {
                info!("委托下单成交");
                exec_msg.exec_qty = last_shares.unwrap_or_default() as i32;
                exec_msg.exec_price = last_px.unwrap_or_default();
                exec_msg.exec_id = exec_id;
                // exec_msg.exec_time = build_transact_time();
                exec_msg.brk_order_id = order_id.parse().unwrap_or_default();
            }
            quickfix_msg42::field_types::OrdStatus::DoneForDay => todo!(),
            quickfix_msg42::field_types::OrdStatus::Canceled | quickfix_msg42::field_types::OrdStatus::PendingCancel => {
                info!("委托撤单成功");
                // PendingCancel 撤单确认
                exec_msg.exec_qty = (order_qty.unwrap_or_default() - deal_qty) as i32;
            }
            quickfix_msg42::field_types::OrdStatus::Replaced => todo!(),
            // quickfix_msg42::field_types::OrdStatus::PendingCancel => todo!(),
            quickfix_msg42::field_types::OrdStatus::Stopped => todo!(),
            quickfix_msg42::field_types::OrdStatus::Rejected => {
                info!("委托下单拒绝");
                exec_msg.memo = test.unwrap_or_default();
            }
            quickfix_msg42::field_types::OrdStatus::Suspended => todo!(),
            quickfix_msg42::field_types::OrdStatus::PendingNew => todo!(),
            quickfix_msg42::field_types::OrdStatus::Calculated => todo!(),
            quickfix_msg42::field_types::OrdStatus::Expired => {
                info!("委托过期"); //交易所拒绝
            }
            quickfix_msg42::field_types::OrdStatus::AcceptedForBidding => todo!(),
            quickfix_msg42::field_types::OrdStatus::PendingReplace => todo!(),
        }

        msg_content.exec_msg = Some(exec_msg);
        resp.msg_type = MsgType::Exec as i32;
        resp.msg_content = Some(msg_content);
        let _ = self.tx_repay_to_router.send(resp);
    }

    /// 处理撤单拒绝
    pub fn process_fix_cancel_order_reject(&self, msg: &OrderCancelReject) {
        let mut resp = RouterMsg::default();
        let mut exec_msg = ExecMsg::default();
        let mut msg_content = MsgContent::default();

        exec_msg.order_id = msg.get_orig_cl_ord_id().parse().unwrap_or_default();
        exec_msg.channel_id = 0;
        exec_msg.channel_type = 1;

        exec_msg.cancel_id = msg.get_cl_ord_id().parse().unwrap_or_default();

        exec_msg.exec_type = ExecType::Rejected as i32;
        exec_msg.memo = msg.get_text().unwrap_or("".to_string());
        // info!("撤单拒绝消息: {:?}", );

        msg_content.exec_msg = Some(exec_msg);
        resp.msg_type = MsgType::Exec as i32;
        resp.msg_content = Some(msg_content);
        let _ = self.tx_repay_to_router.send(resp);
    }
}

pub fn build_transact_time() -> String {
    let now = chrono::Utc::now();
    now.format("%Y%m%d-%T%.3f").to_string()
}

pub fn get_exchange_code(exchange_id: i32) -> String {
    // 101 =>  + "XSHG",
    // 102 =>  + "XSHE",
    // 103 =>  + "XHKG",        //港交所
    // 104 =>  + "XASE",        //美交所
    // 105 =>  + "XNYS",        //纽交所
    // 106 =>  + "XNAS",        //纳斯达克
    if exchange_id == 102 {
        return "XSHE".to_string();
    } else if exchange_id == 103 {
        return "XHKG".to_owned();
    } else if exchange_id == 104 {
        return "XASE".to_owned();
    } else if exchange_id == 105 {
        return "XNYS".to_owned();
    } else if exchange_id == 106 {
        return "XNAS".to_owned();
    }
    return "XSHG".to_owned(); //101
}
