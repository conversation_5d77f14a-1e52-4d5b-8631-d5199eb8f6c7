use parking_lot::RwLock;
use quickfix::*;
use quickfix_msg42::{
    Messages, NewOrderSingle, OrderCancelRequest, field_id,
    field_types::EncryptMethod,
    field_types::{HandlInst, OrdType, Side},
};
use server_protoes::OrderMsg;
use std::{str::FromStr, sync::Arc};
use tracing::{error, info};

use crate::{TRADEHANDLER, build_transact_time, get_exchange_code};

pub static USER_LOGON_REQUEST: &str = "UF001";
pub static USER_LOGON_RESPONSE: &str = "UF002";
pub static LOGON_STATUS: i32 = 8002;

#[derive(Default, Debug)]
pub struct YhFixTradeClient {
    pub session_id: Arc<RwLock<Option<SessionId>>>,
    pub sender_comp_id: RwLock<String>,
    pub target_comp_id: RwLock<String>,
    pub _account: String,
    pub _password: String,
    pub _client_id: String,
}

unsafe impl Send for YhFixTradeClient {}

unsafe impl Sync for YhFixTradeClient {}

impl super::FixClientTrait for YhFixTradeClient {}

impl YhFixTradeClient {
    // pub fn run(&self) {
    //     loop {
    //         thread::sleep(Duration::from_secs(10));
    //     }
    // }

    //处理下单消息
    pub fn _req_new_single_order(&self, request: &OrderMsg) -> Result<(), anyhow::Error> {
        info!("下单请求: {:?}", request);
        let cl_ord_id = request.order_id;
        let symbol = request.stock_code.clone();
        let side = Side::from_str(&request.order_direction.to_string()).unwrap_or(Side::Buy);
        let ord_type = OrdType::from_str(&request.price_type.to_string()).unwrap_or(OrdType::Limit);
        let transact_time = build_transact_time();

        let mut new_order_single = NewOrderSingle::try_new(
            cl_ord_id.to_string(),
            HandlInst::AutomatedExecutionNoIntervention,
            symbol,
            side,
            transact_time,
            ord_type,
        )?;
        new_order_single.set_account(self._account.clone())?;
        new_order_single.set_client_id(self._client_id.clone())?;

        new_order_single.set_order_qty(request.order_qty as f64)?;
        new_order_single.set_price(request.order_price as f64)?;
        new_order_single.set_security_exchange(get_exchange_code(request.exchange_id as i32))?; //根据实际调整
        // new_order_single.set_ex_destination(value)
        new_order_single.set_currency(request.currency_no.clone())?;

        let session_id = SessionId::try_new("FIX.4.2", self.sender_comp_id.read().as_str(), self.target_comp_id.read().as_str(), "")?;
        match send_to_target(new_order_single.into(), &session_id) {
            Ok(_) => info!("下单请求发送成功: {}", request.order_id),
            Err(err) => {
                error!("下单请求发送失败: {} -- {:?}", request.order_id, err);
                // return Err(anyhow::anyhow!("下单请求发送失败: {:?}", err));
            }
        }

        Ok(())
    }

    //处理撤单消息
    pub fn _req_order_cancel(&self, request: &OrderMsg) -> Result<(), anyhow::Error> {
        info!("撤单请求: {:?}", request);
        let cl_ord_id = request.cancel_id;
        let orig_cl_ord_id = request.order_id;
        let symbol = request.stock_code.clone();
        let side = Side::from_str(&request.order_direction.to_string()).unwrap_or(Side::Buy);
        let transact_time = build_transact_time();

        let mut order_cancel = OrderCancelRequest::try_new(orig_cl_ord_id.to_string(), cl_ord_id.to_string(), symbol, side, transact_time)?;
        order_cancel.set_account(self._account.clone())?;
        order_cancel.set_client_id(self._client_id.clone())?;
        order_cancel.set_order_id(request.brk_order_id.clone())?;

        let session_id = SessionId::try_new("FIX.4.2", self.sender_comp_id.read().as_str(), self.target_comp_id.read().as_str(), "")?;
        match send_to_target(order_cancel.into(), &session_id) {
            Ok(_) => info!("撤单请求发送成功：{}", request.order_id),
            Err(err) => {
                error!("撤单请求发送失败: {} -- {:?}", request.order_id, err);
                // return Err(anyhow::anyhow!("撤单请求发送失败: {:?}", err));
            }
        }

        Ok(())
    }

    pub fn logon_verification(&self) -> Result<(), anyhow::Error> {
        info!("登录验证");
        let mut inner = quickfix::Message::new();
        inner.with_header_mut(|h| h.set_field(field_id::MSG_TYPE, USER_LOGON_REQUEST))?;
        inner.set_field(8088, 0)?;
        inner.set_field(field_id::CLIENT_ID, self._client_id.clone())?;
        inner.set_field(field_id::ENCRYPT_METHOD, EncryptMethod::None)?;
        inner.set_field(8001, self._password.clone())?;

        let session_id: SessionId = SessionId::try_new("FIX.4.2", self.sender_comp_id.read().as_str(), self.target_comp_id.read().as_str(), "")?;
        match send_to_target(inner, &session_id) {
            Ok(_) => info!("发送验证消息"),
            Err(err) => {
                error!("发送验证消息失败: {:?}", err);
                // return Err(anyhow::anyhow!("撤单请求发送失败: {:?}", err));
            }
        }
        Ok(())
    }
}

impl ApplicationCallback for YhFixTradeClient {
    /// On session created.
    fn on_create(&self, session: &SessionId) {
        info!("Created Session: {:?}", session);
    }

    /// On session logon.
    fn on_logon(&self, session: &SessionId) {
        *self.session_id.write() = Some(session.clone());
        *self.sender_comp_id.write() = session.get_sender_comp_id().unwrap_or_default();
        *self.target_comp_id.write() = session.get_target_comp_id().unwrap_or_default();
        // self.account = session.get_account().to_string();
        info!("登录成功: {:?}", session);
        info!("Sender comp ID: {}", session.get_sender_comp_id().unwrap_or_default());
        info!("Target comp ID: {}", session.get_target_comp_id().unwrap_or_default());
        let _ = self.logon_verification();
    }

    /// On session logout.
    fn on_logout(&self, session: &SessionId) {
        info!("登录失败: {:?}", session);
    }

    /// Called before sending message to admin level.
    ///
    /// Message can be updated at this stage.
    fn on_msg_to_admin(&self, msg: &mut Message, session: &SessionId) {
        let msg_type = msg.clone_header().get_field(field_id::MSG_TYPE).unwrap_or_default();

        let message = msg.to_fix_string().unwrap_or_default();
        info!(
            "to admin: Sender->Target: {}->{}",
            session.get_sender_comp_id().unwrap_or_default(),
            session.get_target_comp_id().unwrap_or_default()
        );
        info!("to admin: {:?} msg_type: {:?}, 原消息体: {}", session, msg_type, message);
        match Messages::decode(msg.clone()) {
            Ok(Messages::Logon(mut x)) => {
                info!("to admin: 请求验证用户消息： msg_type = {:?}", x.header().get_msg_type());
            }
            Ok(_msg) => {} //info!("{msg:?}"),
            Err(err) => error!("Cannot decode message: {err:?}"),
        }
    }

    /// Called before sending message to application level.
    ///
    /// Message can be updated at this stage.
    fn on_msg_to_app(&self, _msg: &mut Message, _session: &SessionId) -> Result<(), MsgToAppError> {
        Ok(())
    }

    /// Called after received a message from admin level.
    fn on_msg_from_admin(&self, msg: &Message, _session: &SessionId) -> Result<(), MsgFromAdminError> {
        let message = msg.to_fix_string().unwrap_or_default();
        info!("from admin: 消息体：{}", message);
        Ok(())
    }

    /// Called after received a message from application level.
    fn on_msg_from_app(&self, _msg: &Message, _session: &SessionId) -> Result<(), MsgFromAppError> {
        info!("from app: sessionid: {:?}", _session);
        let msg_type = _msg.clone_header().get_field(field_id::MSG_TYPE).unwrap_or_default();
        let message = _msg.to_fix_string().unwrap_or_default();
        info!("from app: 接收柜台下单消息- {:?} msg_type: {:?}, 原消息体: {}", _session, msg_type, message);

        let msg_type = _msg.with_header(|h| h.get_field(field_id::MSG_TYPE));
        if msg_type == Some(USER_LOGON_RESPONSE.to_owned()) {
            let status = _msg.get_field(LOGON_STATUS);
            info!("登录状态：{:?}", status);
        }

        match Messages::decode(_msg.clone()) {
            Ok(Messages::ExecutionReport(x)) => {
                info!("from app: 委托订单处理：{:?}", x);
                match TRADEHANDLER.get() {
                    Some(handler) => handler.process_fix_new_order_rsp(&x),
                    None => {
                        error!("TRADEHANDLER is None");
                    }
                }
            }
            Ok(Messages::OrderCancelReject(x)) => {
                info!("from app: 撤单拒绝消息: {:?}", x);
                match TRADEHANDLER.get() {
                    Some(handler) => handler.process_fix_cancel_order_reject(&x),
                    None => {
                        error!("TRADEHANDLER is None");
                    }
                }
            }
            Ok(Messages::Logon(x)) => {
                info!("from app: 登录消息: {:?}", x);
            }
            Ok(_msg) => {} //info!("{msg:?}"),
            Err(err) => error!("Cannot decode message: {err:?}"),
        }

        Ok(())
    }
}
