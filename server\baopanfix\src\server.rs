use std::sync::Arc;

use crate::{
    FixTrade<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TradeController,
    fixclient::{FixTradeSer, TFixTradeClient, YhFixTradeClient},
    init_trade_handler, order_from_router_to_fix,
};
use async_channel::unbounded;
use orderrouterclient::OrderRouterClientV2;
use server_protoes::RouterMsg;
use tracing::{error, info};

pub struct ServerHandler;

impl ServerHandler {
    pub async fn run(setting: &Settings) {
        //路由中心 ------>
        let (tx_order_from_router, rx_order_from_router) = unbounded::<RouterMsg>();
        // ------> 路由中心
        let (tx_repay_to_router, rx_repay_to_router) = unbounded::<RouterMsg>();

        let handler = FixTradeRspHandler {
            tx_repay_to_router: tx_repay_to_router.clone(),
        };
        init_trade_handler(&handler);

        let mut orderrouterclient = OrderRouterClientV2::new(
            &setting.orderrouter.to_string(),
            &setting.channel,
            tx_order_from_router.clone(),
            rx_repay_to_router.clone(),
        )
        .await;
        let mut retry_interval = tokio::time::interval(std::time::Duration::from_secs(3));
        let tx = tx_repay_to_router.clone();
        tokio::spawn(async move {
            retry_interval.tick().await;
            loop {
                tokio::select! {
                    _ = retry_interval.tick() => {
                        if let Err(err) = orderrouterclient.order_transfer(tx.clone()).await {
                            error!("{:?}", err);
                        }
                    }
                }
            }
        });

        // FIX客户端 ------>
        // tokio::spawn(async move {
        //     if let Err(err) = FixTradeSer::init_fix(&*c) {
        //         error!("Error starting FIX client: {}", err);
        //     }
        // });
        // 用 spawn_blocking 包裹同步初始化
        // FixTradeSer::init_fix(&*fix_client)?;
        // thread::sleep(Duration::from_secs(10));
        let tfix_client = Arc::new(TFixTradeClient::default());
        let c = tfix_client.clone();
        tokio::task::spawn_blocking(move || {
            let config_file = "./config/client.ini";
            if let Err(err) = FixTradeSer::init_fix_client(&*c, config_file) {
                error!("Error starting T FIX client: {}", err);
            }
        });
        let yhfix_client = Arc::new(YhFixTradeClient::default());
        let c = yhfix_client.clone();
        tokio::task::spawn_blocking(move || {
            let config_file = "./config/yhclient.ini";
            if let Err(err) = FixTradeSer::init_fix_client(&*c, config_file) {
                error!("Error starting YH FIX client: {}", err);
            }
        });

        // info!("FIX client started 1: {:#?}", tfix_client);
        let trade_ctl = TradeController {
            tfix: tfix_client,
            yhfix: yhfix_client,
        };

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    result = rx_order_from_router.recv() => {
                        if let Ok(order) = result {
                            info!("收到订单：{:?}", order);
                            if let Some(order) = order_from_router_to_fix(&order).await {
                                trade_ctl.send_fix(order).await;
                            }
                        }
                    }
                }
            }
        });
    }
}
