#ifndef CONFIG_H_IN
#define CONFIG_H_IN

#ifdef _MSC_VER
#include "config_windows.h"
#else
#include "config_unix.h"
#endif

/* Define to 1 if you have SSL support */
#cmakedefine HAVE_SSL @HAVE_SSL@

/* Define to 1 if you have MySQL support */
#cmakedefine HAVE_MYSQL @HAVE_MYSQL@

/* Define to 1 if you have PostgreSQL support */
#cmakedefine HAVE_POSTGRESQL @HAVE_POSTGRESQL@

/* Define to 1 if you have Python support */
#cmakedefine HAVE_PYTHON @HAVE_PYTHON@

/* Define to 1 if you have Python3 support */
#cmakedefine HAVE_PYTHON3 @HAVE_PYTHON3@

/* Define to 1 if you have shared library support */
#cmakedefine QUICKFIX_SHARED_LIBS @QUICKFIX_SHARED_LIBS@

/* Define to 1 if you want to build examples */
#cmakedefine QUICKFIX_EXAMPLES @QUICKFIX_EXAMPLES@

/* Define to 1 if you want to build tests */
#cmakedefine QUICKFIX_TESTS @QUICKFIX_TESTS@

#endif
