use config::{Config, ConfigError, File};
use serde::Deserialize;

#[derive(Debug, <PERSON><PERSON>, Deserialize)]
#[allow(dead_code)]
pub struct Settings {
    pub orderrouter: String,
    pub channel: Vec<i64>,
    pub username: String,
    pub password: String,
    pub account: String,
}

impl Settings {
    pub fn init(cfgfile: &str) -> Result<Self, ConfigError> {
        let s = Config::builder().add_source(File::with_name(cfgfile)).build().expect("build config file error");

        s.try_deserialize()
    }
}
